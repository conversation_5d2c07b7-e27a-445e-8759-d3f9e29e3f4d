# SonarQube Project Configuration for Healtether.Clinics API
# This file configures static code analysis for the Clinics API

# Project identification
sonar.projectKey=healtether-clinics-api
sonar.projectName=Healtether Clinics API
sonar.projectVersion=1.0.0
sonar.projectDescription=Healthcare Clinics Management API - Static Code Analysis

# SonarQube server configuration
sonar.host.url=http://localhost:9000
sonar.token=sqp_9833dac32acc6233060116e797a16d7c314fcf5c

# Source code configuration
sonar.sources=.
sonar.exclusions=node_modules/**,coverage/**,dist/**,build/**,*.log,package-lock.json,yarn.lock

# Language and file patterns
sonar.javascript.file.suffixes=.js,.jsx
sonar.typescript.file.suffixes=.ts,.tsx
sonar.json.file.suffixes=.json

# Test configuration
sonar.tests=__tests__
sonar.test.inclusions=**/*test.js,**/*spec.js,**/*.test.js,**/*.spec.js
sonar.test.exclusions=node_modules/**

# Coverage configuration (if you have test coverage)
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*test.js,**/*spec.js,**/*.test.js,**/*.spec.js,**/node_modules/**

# Security and quality profiles
sonar.qualitygate.wait=true

# Encoding
sonar.sourceEncoding=UTF-8

# Additional analysis parameters
sonar.analysis.mode=publish

# Exclude specific directories and files
sonar.exclusions=\
  **/node_modules/**,\
  **/coverage/**,\
  **/dist/**,\
  **/build/**,\
  **/*.min.js,\
  **/vendor/**,\
  **/third-party/**,\
  **/*.log,\
  **/logs/**,\
  **/temp/**,\
  **/tmp/**,\
  **/.env,\
  **/*.env

# Include specific file patterns for analysis
sonar.inclusions=\
  **/*.js,\
  **/*.jsx,\
  **/*.ts,\
  **/*.tsx,\
  **/*.json

# Security hotspot configuration
sonar.security.hotspots.inheritFromParent=true

# Duplication configuration
sonar.cpd.exclusions=**/*test.js,**/*spec.js

# Analysis parameters for Node.js/Express with Socket.IO
sonar.javascript.environments=node,browser
sonar.javascript.globals=global,process,Buffer,__dirname,__filename,module,require,exports,console,io

# Custom rules and quality profiles
# These will be configured in the SonarQube UI after setup

# FHIR Standard HTTP URLs Exclusions
# These HTTP URLs are required by FHIR healthcare standards and cannot be changed to HTTPS
# They are not security vulnerabilities but standard namespace URIs for medical interoperability

# Security exclusions for legitimate use cases
# - FHIR files: HTTP URLs are required by healthcare standards (not security issues)
# - .env files: Designed to contain sensitive data (proper security practice)
sonar.security.exclusions=**/helper/fhir/**/*.js,**/utils/fhir.constants.js,**/temp_invoice_report.js,**/.env,**/*.env

# Backup approach: Issue-level exclusions
sonar.issue.ignore.multicriteria=e1

# Exclude S5332 (HTTP URLs) from all FHIR-related files
sonar.issue.ignore.multicriteria.e1.ruleKey=javascript:S5332
sonar.issue.ignore.multicriteria.e1.resourceKey=**/helper/fhir/**

# Socket.IO specific exclusions (if needed)
# sonar.issue.ignore.multicriteria=e6
# sonar.issue.ignore.multicriteria.e6.ruleKey=javascript:S*
# sonar.issue.ignore.multicriteria.e6.resourceKey=**/socket.io/**
